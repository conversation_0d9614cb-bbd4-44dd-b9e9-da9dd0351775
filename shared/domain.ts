// Shared domain enums generated from Prisma schema
// This file MUST stay in sync with prisma/schema.prisma.
// Both backend (ts-node) and frontend (Vite) import from here to avoid string-drift bugs.

export enum Role {
	PATIENT = 'PATIENT',
	PROVIDER = 'PROVIDER',
	DOCTOR = 'DOCTOR',
	ADMIN = 'ADMIN',
}

export enum ConsultationType {
	VIDEO = 'VIDEO',
	AUDIO = 'AUDIO',
}

export enum AppointmentStatus {
	SCHEDULED = 'SCHEDULED',
	CONFIRMED = 'CONFIRMED',
	COMPLETED = 'COMPLETED',
	CANCELLED = 'CANCELLED',
	NO_SHOW = 'NO_SHOW',
}

export enum PaymentStatus {
	INITIATED = 'INITIATED',
	PENDING = 'PENDING',
	SUCCEEDED = 'SUCCEEDED',
	FAILED = 'FAILED',
	REFUNDED = 'REFUNDED',
}

// Utility: runtime guard helpers
export const isConsultationType = (v: unknown): v is ConsultationType =>
	v === ConsultationType.VIDEO || v === ConsultationType.AUDIO;

export const isAppointmentStatus = (v: unknown): v is AppointmentStatus =>
	Object.values(AppointmentStatus).includes(v as AppointmentStatus);
