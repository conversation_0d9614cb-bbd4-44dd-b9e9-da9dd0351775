<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug API Configuration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 API Configuration Debug Tool</h1>
        <p>This tool helps debug the frontend-backend connection issues.</p>

        <div class="test-section info">
            <h3>Environment Variables</h3>
            <div id="env-vars">Loading...</div>
        </div>

        <div class="test-section">
            <h3>API Connectivity Tests</h3>
            <button onclick="testLocalhost()">Test Localhost API</button>
            <button onclick="testRemoteIP()">Test Remote IP API</button>
            <button onclick="testLogin()">Test Login</button>
            <div id="api-results"></div>
        </div>

        <div class="test-section">
            <h3>Frontend Configuration</h3>
            <div id="frontend-config">Loading...</div>
        </div>
    </div>

    <script>
        // Display environment info
        function showEnvironmentInfo() {
            const envDiv = document.getElementById('env-vars');
            const info = {
                'Current URL': window.location.href,
                'User Agent': navigator.userAgent,
                'Protocol': window.location.protocol,
                'Host': window.location.host,
                'Port': window.location.port || 'default'
            };
            
            let html = '<pre>';
            for (const [key, value] of Object.entries(info)) {
                html += `${key}: ${value}\n`;
            }
            html += '</pre>';
            envDiv.innerHTML = html;
        }

        // Test localhost API
        async function testLocalhost() {
            await testAPI('http://localhost:3000/api/auth/login', 'Localhost API');
        }

        // Test remote IP API
        async function testRemoteIP() {
            await testAPI('http://*************:3000/api/auth/login', 'Remote IP API');
        }

        // Generic API test
        async function testAPI(baseUrl, label) {
            const resultsDiv = document.getElementById('api-results');
            resultsDiv.innerHTML += `<h4>Testing ${label}...</h4>`;
            
            try {
                // Test health endpoint first
                const healthResponse = await fetch(baseUrl.replace('/auth/login', '/health'), {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (healthResponse.ok) {
                    const healthData = await healthResponse.json();
                    resultsDiv.innerHTML += `<div class="test-section success">
                        <strong>✅ ${label} Health Check: SUCCESS</strong>
                        <pre>${JSON.stringify(healthData, null, 2)}</pre>
                    </div>`;
                } else {
                    resultsDiv.innerHTML += `<div class="test-section error">
                        <strong>❌ ${label} Health Check: FAILED</strong>
                        <p>Status: ${healthResponse.status} ${healthResponse.statusText}</p>
                    </div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML += `<div class="test-section error">
                    <strong>❌ ${label} Connection: FAILED</strong>
                    <p>Error: ${error.message}</p>
                </div>`;
            }
        }

        // Test login with actual credentials
        async function testLogin() {
            const resultsDiv = document.getElementById('api-results');
            resultsDiv.innerHTML += `<h4>Testing Login with Real Credentials...</h4>`;
            
            const credentials = {
                email: '<EMAIL>',
                password: 'Sinead12.'
            };
            
            // Test both localhost and remote
            const endpoints = [
                'http://localhost:3000/api/auth/login',
                'http://*************:3000/api/auth/login'
            ];
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(credentials)
                    });
                    
                    const data = await response.json();
                    
                    if (response.ok && data.success) {
                        resultsDiv.innerHTML += `<div class="test-section success">
                            <strong>✅ Login Success: ${endpoint}</strong>
                            <p>User: ${data.data.user.name} (${data.data.user.email})</p>
                            <p>Token: ${data.data.token.substring(0, 20)}...</p>
                        </div>`;
                    } else {
                        resultsDiv.innerHTML += `<div class="test-section error">
                            <strong>❌ Login Failed: ${endpoint}</strong>
                            <p>Error: ${data.error || 'Unknown error'}</p>
                        </div>`;
                    }
                } catch (error) {
                    resultsDiv.innerHTML += `<div class="test-section error">
                        <strong>❌ Login Error: ${endpoint}</strong>
                        <p>Error: ${error.message}</p>
                    </div>`;
                }
            }
        }

        // Show frontend configuration
        function showFrontendConfig() {
            const configDiv = document.getElementById('frontend-config');
            
            // Try to access the frontend's API configuration
            const config = {
                'Window Location': window.location.href,
                'Expected Frontend': 'http://*************:10000',
                'Expected Backend': 'http://*************:3000/api',
                'CORS Test': 'Will test cross-origin requests'
            };
            
            let html = '<pre>';
            for (const [key, value] of Object.entries(config)) {
                html += `${key}: ${value}\n`;
            }
            html += '</pre>';
            
            configDiv.innerHTML = html;
        }

        // Initialize on page load
        window.onload = function() {
            showEnvironmentInfo();
            showFrontendConfig();
        };
    </script>
</body>
</html>
