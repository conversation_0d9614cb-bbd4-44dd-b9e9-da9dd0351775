# Server Configuration
NODE_ENV=development
PORT=3000
API_BASE_URL=http://localhost:3000/api

# Frontend Configuration
FRONTEND_URL=http://localhost:10000
CORS_ORIGINS=http://localhost:10000,http://localhost:3000

# Database Configuration (Neon PostgreSQL)
DATABASE_URL="postgresql://username:<EMAIL>/database?pgbouncer=true&connect_timeout=10"
DIRECT_URL="postgresql://username:<EMAIL>/database?connect_timeout=10"

# Authentication
JWT_SECRET=fintan_virtual_care_jwt_secret_key_2024_development
JWT_REFRESH_SECRET=fintan_virtual_care_refresh_secret_key_2024_development
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# Daily.co Video/Audio Configuration
DAILY_API_KEY=your-daily-api-key-here
DAILY_DOMAIN=your-daily-domain.daily.co

# Payment Configuration - Stripe
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-stripe-webhook-secret

# Payment Configuration - Paystack
PAYSTACK_SECRET_KEY=sk_test_your-paystack-secret-key

# Payment Configuration - PayPal
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret

# Payment Configuration - Flutterwave
FLUTTERWAVE_SECRET_KEY=FLWSECK_TEST-your-flutterwave-secret-key

# Email Configuration (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME="Dr. Fintan's Practice"

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# Security Configuration
BCRYPT_SALT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Feature Flags
ENABLE_REGISTRATION=true
ENABLE_EMAIL_VERIFICATION=true
ENABLE_SMS_NOTIFICATIONS=true
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_PAYMENT_PROCESSING=true
ENABLE_VIDEO_CONSULTATIONS=true
ENABLE_AUDIO_CONSULTATIONS=true

# File Upload Configuration
MAX_FILE_SIZE=********
ALLOWED_FILE_TYPES=pdf,jpg,jpeg,png,doc,docx

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30000
DATABASE_HEALTH_CHECK_TIMEOUT=5000

# External Service Timeouts
DAILY_API_TIMEOUT=10000
STRIPE_API_TIMEOUT=10000
PAYSTACK_API_TIMEOUT=10000
SMTP_TIMEOUT=10000
TWILIO_TIMEOUT=10000
