<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Bar Popup Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 4px; width: 200px; }
        .step { margin: 10px 0; padding: 10px; border-left: 4px solid #007bff; background: #f8f9fa; }
        .navbar-sim {
            background: white;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 20px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 8px;
            width: 400px;
            max-width: 90%;
        }
        .modal.show { display: block; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Navigation Bar Popup Authentication Test</h1>
        <p>This simulates the exact flow from the navigation bar login popup</p>

        <!-- Simulated Navigation Bar -->
        <div class="navbar-sim">
            <div><strong>Dr. Fintan Virtual Care Hub</strong></div>
            <div>
                <button onclick="openLoginModal()" style="background: #6c757d;">Login</button>
                <button onclick="openSignupModal()" style="background: #28a745;">Sign Up</button>
            </div>
        </div>

        <div class="test-section info">
            <h3>Test Flow Steps</h3>
            <div class="step">1. Click "Login" button above (simulates navbar)</div>
            <div class="step">2. Modal should open with login form</div>
            <div class="step">3. Enter credentials and click "Sign In"</div>
            <div class="step">4. Check if API call is made correctly</div>
        </div>

        <div class="test-section">
            <h3>API Configuration Test</h3>
            <button onclick="testAPIConfig()">Test API Configuration</button>
            <div id="api-config-results"></div>
        </div>

        <div class="test-section">
            <h3>Direct API Test</h3>
            <button onclick="testDirectAPI()">Test Direct Login API</button>
            <div id="direct-api-results"></div>
        </div>

        <div id="test-results"></div>
    </div>

    <!-- Login Modal (Simulates AuthModals component) -->
    <div id="loginModal" class="modal">
        <div class="modal-content">
            <h3>Welcome Back</h3>
            <p>Sign in to your account to continue</p>
            
            <form id="loginForm">
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" value="<EMAIL>" required>
                </div>
                
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" value="Sinead12." required>
                </div>
                
                <div class="form-group">
                    <button type="submit" style="width: 100%; padding: 12px;">Sign In</button>
                    <button type="button" onclick="closeLoginModal()" style="background: #6c757d; width: 100%; padding: 12px; margin-top: 10px;">Cancel</button>
                </div>
            </form>
            
            <div id="modal-results"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://*************:3000/api';
        
        function addResult(containerId, title, content, isSuccess = true) {
            const container = document.getElementById(containerId);
            const className = isSuccess ? 'success' : 'error';
            container.innerHTML += `
                <div class="test-section ${className}">
                    <h4>${title}</h4>
                    <pre>${typeof content === 'object' ? JSON.stringify(content, null, 2) : content}</pre>
                </div>
            `;
        }

        function openLoginModal() {
            console.log('Opening login modal...');
            document.getElementById('loginModal').classList.add('show');
            addResult('test-results', 'Modal Opened', 'Login modal opened successfully', true);
        }

        function closeLoginModal() {
            document.getElementById('loginModal').classList.remove('show');
            addResult('test-results', 'Modal Closed', 'Login modal closed', true);
        }

        function openSignupModal() {
            alert('Signup modal would open here (not implemented in test)');
        }

        async function testAPIConfig() {
            const config = {
                'API_BASE_URL': API_BASE_URL,
                'Login Endpoint': `${API_BASE_URL}/auth/login`,
                'Expected Frontend': 'http://*************:10000',
                'Current Origin': window.location.origin
            };
            
            addResult('api-config-results', 'API Configuration', config, true);
        }

        async function testDirectAPI() {
            try {
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Origin': 'http://*************:10000'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'Sinead12.'
                    })
                });
                
                const data = await response.json();
                
                addResult('direct-api-results', 'Direct API Test', {
                    status: response.status,
                    success: data.success,
                    user: data.data?.user?.name || 'No user',
                    hasToken: !!data.data?.token,
                    error: data.error || 'None'
                }, response.ok && data.success);
                
            } catch (error) {
                addResult('direct-api-results', 'Direct API Test', `Error: ${error.message}`, false);
            }
        }

        // Handle login form submission (simulates AuthModals handleLogin)
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            if (!email || !password) {
                addResult('modal-results', 'Validation Error', 'Please enter both email and password', false);
                return;
            }
            
            try {
                addResult('modal-results', 'Login Attempt', `Attempting login for ${email}...`, true);
                
                // This simulates the exact same call that AuthModals makes
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    addResult('modal-results', 'Login Success!', {
                        user: data.data.user.name,
                        email: data.data.user.email,
                        role: data.data.user.role,
                        tokenReceived: !!data.data.token
                    }, true);
                    
                    // Simulate successful login actions
                    setTimeout(() => {
                        closeLoginModal();
                        addResult('test-results', 'Login Complete', 'User would now be logged in and modal closed', true);
                    }, 2000);
                    
                } else {
                    addResult('modal-results', 'Login Failed', {
                        status: response.status,
                        error: data.error || 'Unknown error',
                        success: data.success
                    }, false);
                }
                
            } catch (error) {
                addResult('modal-results', 'Network Error', `Failed to connect: ${error.message}`, false);
            }
        });

        // Auto-run configuration test
        window.onload = function() {
            testAPIConfig();
        };
    </script>
</body>
</html>
