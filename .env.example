# Neon PostgreSQL Connection
# IMPORTANT: Replace these placeholder values with your actual credentials

# Development Database URLs
DATABASE_URL="postgres://[YOUR_USERNAME]:[YOUR_PASSWORD]@[YOUR_NEON_HOST].neon.tech/[YOUR_DATABASE]?pgbouncer=true&connect_timeout=10"
DIRECT_URL="postgres://[YOUR_USERNAME]:[YOUR_PASSWORD]@[YOUR_NEON_HOST].neon.tech/[YOUR_DATABASE]?connect_timeout=10"

# Production Database URLs (for Render, Vercel, etc.)
# DATABASE_URL="postgres://[YOUR_USERNAME]:[YOUR_PASSWORD]@[YOUR_NEON_HOST].neon.tech/[YOUR_DATABASE]?pgbouncer=true&connect_timeout=10"
# DIRECT_URL="postgres://[YOUR_USERNAME]:[YOUR_PASSWORD]@[YOUR_NEON_HOST].neon.tech/[YOUR_DATABASE]?connect_timeout=10"

# IMPORTANT: For Neon Serverless PostgreSQL
# - DATABASE_URL should use pgbouncer=true for connection pooling
# - DIRECT_URL should connect directly without pgbouncer for migrations
# - Both are required for Prisma to work correctly with Neon

# Application Settings
NODE_ENV="production" # Use "production" for deployment
PORT=3000

# API Configuration
VITE_API_URL=http://localhost:3000/api
VITE_APP_URL=http://localhost:5173

# Authentication
JWT_SECRET=your-super-secret-jwt-key-here
JWT_REFRESH_SECRET=your-super-secret-refresh-key-here
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d

# Daily.co Video/Audio Configuration
DAILY_API_KEY=your-daily-api-key-here
DAILY_DOMAIN=your-daily-domain.daily.co
VITE_DAILY_DOMAIN=your-daily-domain.daily.co

# Payment Configuration - Stripe
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-stripe-webhook-secret
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key

# Payment Configuration - Paystack
PAYSTACK_SECRET_KEY=sk_test_your-paystack-secret-key
VITE_PAYSTACK_PUBLIC_KEY=pk_test_your-paystack-public-key

# Payment Configuration - PayPal
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret
VITE_PAYPAL_CLIENT_ID=your-paypal-client-id

# Payment Configuration - Flutterwave
FLUTTERWAVE_SECRET_KEY=FLWSECK_TEST-your-flutterwave-secret-key
VITE_FLUTTERWAVE_PUBLIC_KEY=FLWPUBK_TEST-your-flutterwave-public-key

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME="Dr. Fintan's Practice"

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# Feature Flags
# Frontend flags (accessible in browser)
VITE_ENABLE_VIDEO_CALLS=true
VITE_ENABLE_AUDIO_CALLS=true
VITE_ENABLE_PAYMENTS=true

# Backend flags (server-side only)
ENABLE_REGISTRATION=true
ENABLE_SCREEN_SHARING=true
ENABLE_CALL_RECORDING=true
ENABLE_CALENDAR_INTEGRATION=true
ENABLE_SMS_NOTIFICATIONS=true
ENABLE_EMAIL_NOTIFICATIONS=true

# Default Settings
VITE_DEFAULT_PROVIDER_ID=default-provider-id
VITE_DEFAULT_VIDEO_PRICE=85
VITE_DEFAULT_AUDIO_PRICE=65







# OAuth/SSO Configuration
# Google OAuth
VITE_GOOGLE_CLIENT_ID="[YOUR_GOOGLE_CLIENT_ID]"
GOOGLE_CLIENT_SECRET="[YOUR_GOOGLE_CLIENT_SECRET]"

# Microsoft OAuth
VITE_MICROSOFT_CLIENT_ID="[YOUR_MICROSOFT_CLIENT_ID]"
MICROSOFT_CLIENT_SECRET="[YOUR_MICROSOFT_CLIENT_SECRET]"

# Apple OAuth
VITE_APPLE_CLIENT_ID="[YOUR_APPLE_CLIENT_ID]"
APPLE_CLIENT_SECRET="[YOUR_APPLE_CLIENT_SECRET]"





# Notification Service
NOTIFICATION_API_CLIENT_ID="[YOUR_NOTIFICATION_API_CLIENT_ID]"
NOTIFICATION_API_CLIENT_SECRET="[YOUR_NOTIFICATION_API_CLIENT_SECRET]"

# Security and Rate Limiting
# For development (localhost only)
CORS_ORIGIN="http://localhost:3000,http://localhost:8080"

# For production (update with your domains)
# CORS_ORIGIN="https://[YOUR_PRODUCTION_DOMAIN],https://admin.[YOUR_PRODUCTION_DOMAIN]"

RATE_LIMIT_WINDOW_MS=900000 # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100 # Maximum requests per window

# Logging
LOG_LEVEL="info" # debug, info, warn, error



# Deployment Settings
# Set to true to run migrations during build (for platforms like Render)
RUN_MIGRATIONS_ON_BUILD=true

