// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String?   @unique
  emailVerified DateTime?
  image         String?
  password      String?
  role          Role    @default(PATIENT)
  phone         String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  accounts      Account[]
  sessions      Session[]
  patient       Patient?
  provider      Provider?
  notifications Notification[]
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Patient {
  id        String   @id @default(cuid())
  userId    String   @unique
  firstName  String?
  lastName   String?
  dateOfBirth DateTime?
  phone      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  appointments Appointment[]
}

model Provider {
  id             String   @id @default(cuid())
  userId         String   @unique
  specialization String?
  bio            String?  @db.Text
  avatarUrl      String?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  appointments Appointment[]
}

model Appointment {
  id              String   @id @default(cuid())
  providerId      String
  patientId       String
  appointmentDate DateTime
  reason          String?  @db.Text
  status          AppointmentStatus   @default(SCHEDULED)
  consultationType ConsultationType @default(VIDEO) // VIDEO or AUDIO
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  provider     Provider      @relation(fields: [providerId], references: [id], onDelete: Cascade)
  patient      Patient       @relation(fields: [patientId], references: [id], onDelete: Cascade)
  consultation Consultation?
  payment      Payment?
}

model Consultation {
  id           String   @id @default(cuid())
  appointmentId String   @unique
  roomUrl      String
  status       String   @default("SCHEDULED")
  videoEnabled Boolean  @default(false) // Tracks if video was enabled in an audio call
  notes        String?  @db.Text
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  appointment Appointment @relation(fields: [appointmentId], references: [id], onDelete: Cascade)
}

model Notification {
  id        String   @id @default(cuid())
  title     String
  message   String   @db.Text
  type      String
  userId    String
  relatedId String?
  isRead    Boolean  @default(false)
  link      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

// ---------------- Enums ----------------
enum Role {
  PATIENT
  PROVIDER
  DOCTOR
  ADMIN
}

enum ConsultationType {
  VIDEO
  AUDIO
}

enum AppointmentStatus {
  SCHEDULED
  CONFIRMED
  COMPLETED
  CANCELLED
  NO_SHOW
}

enum PaymentStatus {
  INITIATED
  PENDING
  SUCCEEDED
  FAILED
  REFUNDED
}

// ---------------- Payment Model ----------------
model Payment {
  id            String        @id @default(cuid())
  appointmentId String        @unique
  amount        Int
  currency      String        @default("USD")
  provider      String
  status        PaymentStatus @default(PENDING)
  reference     String?
  receiptUrl    String?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  appointment   Appointment   @relation(fields: [appointmentId], references: [id], onDelete: Cascade)
}
