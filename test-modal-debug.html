<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modal Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 4px; width: 200px; }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 8px;
            width: 400px;
            max-width: 90%;
        }
        .modal.show { display: block; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Modal & Login Debug Test</h1>
        <p>Test the login modal functionality step by step</p>

        <div class="test-section info">
            <h3>Step 1: Test Modal Functionality</h3>
            <button onclick="showTestModal()">Show Test Modal</button>
            <button onclick="hideTestModal()">Hide Test Modal</button>
            <p>This tests if modals work in general</p>
        </div>

        <div class="test-section">
            <h3>Step 2: Test API Connection</h3>
            <button onclick="testAPIConnection()">Test Backend Health</button>
            <button onclick="testLoginAPI()">Test Login API</button>
            <div id="api-results"></div>
        </div>

        <div class="test-section">
            <h3>Step 3: Test Frontend Navigation</h3>
            <p>Go to the actual frontend and test:</p>
            <a href="http://*************:10000" target="_blank" style="color: #007bff;">
                Open Frontend Homepage
            </a>
            <p>Then click the "Login" button in the navigation bar</p>
        </div>

        <div class="test-section">
            <h3>Step 4: Manual Login Test</h3>
            <div>
                <input type="email" id="test-email" placeholder="Email" value="<EMAIL>">
                <input type="password" id="test-password" placeholder="Password" value="Sinead12.">
                <button onclick="testManualLogin()">Manual Login Test</button>
            </div>
            <div id="manual-results"></div>
        </div>

        <div id="debug-results"></div>
    </div>

    <!-- Test Modal -->
    <div id="testModal" class="modal">
        <div class="modal-content">
            <h3>Test Modal</h3>
            <p>This is a test modal to verify modal functionality works.</p>
            <button onclick="hideTestModal()">Close</button>
        </div>
    </div>

    <script>
        const API_BASE = 'http://*************:3000';
        
        function addResult(containerId, title, content, isSuccess = true) {
            const container = document.getElementById(containerId);
            const className = isSuccess ? 'success' : 'error';
            container.innerHTML += `
                <div class="test-section ${className}">
                    <h4>${title}</h4>
                    <pre>${typeof content === 'object' ? JSON.stringify(content, null, 2) : content}</pre>
                </div>
            `;
        }

        function showTestModal() {
            document.getElementById('testModal').classList.add('show');
        }

        function hideTestModal() {
            document.getElementById('testModal').classList.remove('show');
        }

        async function testAPIConnection() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                addResult('api-results', 'Backend Health Check', data, response.ok);
            } catch (error) {
                addResult('api-results', 'Backend Health Check', `Error: ${error.message}`, false);
            }
        }

        async function testLoginAPI() {
            try {
                const response = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Origin': window.location.origin
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'Sinead12.'
                    })
                });
                
                const data = await response.json();
                addResult('api-results', 'Login API Test', {
                    status: response.status,
                    success: data.success,
                    user: data.data?.user?.name || 'No user data',
                    hasToken: !!data.data?.token
                }, response.ok && data.success);
                
            } catch (error) {
                addResult('api-results', 'Login API Test', `Error: ${error.message}`, false);
            }
        }

        async function testManualLogin() {
            const email = document.getElementById('test-email').value;
            const password = document.getElementById('test-password').value;
            
            try {
                addResult('manual-results', 'Manual Login Attempt', `Trying to login with ${email}...`);
                
                // Test with exact same method as frontend would use
                const response = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    addResult('manual-results', 'Manual Login Success', {
                        user: data.data.user.name,
                        email: data.data.user.email,
                        role: data.data.user.role,
                        tokenLength: data.data.token.length
                    }, true);
                    
                    // Store token for testing
                    localStorage.setItem('test-token', data.data.token);
                    
                } else {
                    addResult('manual-results', 'Manual Login Failed', {
                        status: response.status,
                        error: data.error || 'Unknown error',
                        data: data
                    }, false);
                }
                
            } catch (error) {
                addResult('manual-results', 'Manual Login Error', `Network Error: ${error.message}`, false);
            }
        }

        // Auto-run health check on load
        window.onload = function() {
            testAPIConnection();
        };
    </script>
</body>
</html>
