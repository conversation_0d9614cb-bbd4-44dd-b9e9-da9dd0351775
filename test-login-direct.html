<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct Login Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Direct Login Test</h1>
        <p>Test login functionality directly without the React app</p>

        <div class="test-section">
            <h3>Login Credentials</h3>
            <div>
                <input type="email" id="email" placeholder="Email" value="<EMAIL>">
                <input type="password" id="password" placeholder="Password" value="Sinead12.">
                <button onclick="testDirectLogin()">Test Direct Login</button>
            </div>
        </div>

        <div class="test-section">
            <h3>API Tests</h3>
            <button onclick="testHealth()">Test Health Endpoint</button>
            <button onclick="testCORS()">Test CORS</button>
            <button onclick="testWithCurl()">Test with Fetch (like frontend)</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        const API_BASE = 'http://*************:3000';
        
        function addResult(title, content, isSuccess = true) {
            const resultsDiv = document.getElementById('results');
            const className = isSuccess ? 'success' : 'error';
            resultsDiv.innerHTML += `
                <div class="test-section ${className}">
                    <h4>${title}</h4>
                    <pre>${typeof content === 'object' ? JSON.stringify(content, null, 2) : content}</pre>
                </div>
            `;
        }

        async function testHealth() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                addResult('Health Check', data, response.ok);
            } catch (error) {
                addResult('Health Check', `Error: ${error.message}`, false);
            }
        }

        async function testCORS() {
            try {
                const response = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': window.location.origin,
                        'Access-Control-Request-Method': 'POST',
                        'Access-Control-Request-Headers': 'Content-Type'
                    }
                });
                addResult('CORS Preflight', {
                    status: response.status,
                    headers: Object.fromEntries(response.headers.entries())
                }, response.ok);
            } catch (error) {
                addResult('CORS Preflight', `Error: ${error.message}`, false);
            }
        }

        async function testDirectLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                addResult('Login Attempt', `Trying to login with ${email}...`);
                
                const response = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Origin': window.location.origin
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                addResult('Login Response', {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                }, response.ok && data.success);
                
                if (response.ok && data.success) {
                    addResult('Login Success', `Welcome ${data.data.user.name}! Token: ${data.data.token.substring(0, 20)}...`);
                }
                
            } catch (error) {
                addResult('Login Error', `Network/JS Error: ${error.message}`, false);
            }
        }

        async function testWithCurl() {
            // This simulates what the frontend should be doing
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                addResult('Frontend-style Request', 'Making request like the React app would...');
                
                const response = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    credentials: 'include', // This might be important for CORS
                    body: JSON.stringify({ email, password })
                });
                
                const responseText = await response.text();
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (e) {
                    data = responseText;
                }
                
                addResult('Frontend-style Response', {
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries()),
                    body: data
                }, response.ok);
                
            } catch (error) {
                addResult('Frontend-style Error', `Error: ${error.message}`, false);
            }
        }

        // Auto-run health check on load
        window.onload = function() {
            testHealth();
        };
    </script>
</body>
</html>
