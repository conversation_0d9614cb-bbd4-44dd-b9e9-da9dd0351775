
import React from 'react';

const PhilosophySection = () => {
  return (
    <div className="mb-8">
      <h3 className="text-xl font-semibold mb-4 dark:text-medical-dark-text-primary">What to expect from a virtual consultation with Dr<PERSON></h3>
      <div className="bg-gray-50 dark:bg-gray-800 p-5 rounded-md border-l-4 border-medical-primary dark:border-medical-accent">
        <p className="italic text-medical-neutral-600 dark:text-medical-dark-text-secondary mb-4">
          "Dr <PERSON><PERSON>'s medical practice is an amalgamation of Orthodox and Alternative medicine, yielding a blend of 
          Complementary, Functional, Orthomolecular, and Lifestyle Medicine. This delivers a pharmacologically 
          minimalist approach to healthcare. Most consultations end without a drug prescription, which makes for 
          efficient cross border client care."
        </p>
        <p className="text-medical-neutral-600 dark:text-medical-dark-text-secondary mb-4">
          So, are you in search of a second opinion for a medical consultation? Or do you just wish to get a 
          better effective standard medical advice at affordable cost?
        </p>
        <p className="text-medical-neutral-600 dark:text-medical-dark-text-secondary mb-4">
          Are you a busy executive that can hardly find time to wait in the queue to see a doctor? Do you care to 
          write your will due to soon as called "incurable conditions"?
        </p>
        <p className="text-medical-neutral-600 dark:text-medical-dark-text-secondary mb-4">
          Are you looking for What: Diet? Or What about Nutrient to find your Health?
        </p>
        <p className="text-medical-neutral-600 dark:text-medical-dark-text-secondary mb-4">
          Then you have come to the right place. Welcome!
        </p>
        <p className="mt-3 font-medium dark:text-medical-dark-text-primary">— Dr. Fintan Ekochin</p>
      </div>
    </div>
  );
};

export default PhilosophySection;
