
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 20% 98%;
    --foreground: 215 25% 27%;

    --card: 0 0% 100%;
    --card-foreground: 215 25% 27%;

    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 27%;

    --primary: 197 65% 29%;
    --primary-foreground: 210 40% 98%;

    --secondary: 180 75% 34%;
    --secondary-foreground: 210 40% 98%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 174 53% 56%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 197 65% 29%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 220 20% 10%;
    --foreground: 213 31% 91%;

    --card: 224 71% 12%;
    --card-foreground: 213 31% 91%;

    --popover: 224 71% 12%;
    --popover-foreground: 213 31% 91%;

    --primary: 197 65% 29%;
    --primary-foreground: 210 40% 98%;

    --secondary: 180 75% 34%;
    --secondary-foreground: 0 0% 98%;

    --muted: 223 47% 11%;
    --muted-foreground: 215.4 16.3% 56.9%;

    --accent: 174 53% 56%;
    --accent-foreground: 0 0% 12%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 216 34% 17%;
    --input: 216 34% 17%;
    --ring: 197 65% 29%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    @apply h-full overflow-x-hidden scroll-smooth;
  }

  body {
    @apply bg-background text-foreground font-inter min-h-screen;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-inter font-semibold;
    @apply text-medical-neutral-600 dark:text-medical-dark-text-primary;
  }

  h1 {
    @apply text-3xl lg:text-4xl;
  }

  h2 {
    @apply text-2xl lg:text-3xl;
  }

  h3 {
    @apply text-xl lg:text-2xl;
  }

  h4 {
    @apply text-lg lg:text-xl;
  }
  
  p {
    @apply leading-relaxed text-medical-neutral-600 dark:text-medical-dark-text-secondary;
  }
  
  .lead {
    @apply text-lg text-medical-neutral-500 dark:text-medical-dark-text-secondary;
  }
}

@layer components {
  .btn-primary {
    @apply bg-medical-primary text-white hover:bg-medical-primary/90 dark:bg-medical-accent dark:hover:bg-medical-accent/90 px-6 py-3 rounded-md font-medium transition-colors;
  }
  
  .btn-secondary {
    @apply bg-medical-secondary text-white hover:bg-medical-secondary/90 px-6 py-3 rounded-md font-medium transition-colors;
  }
  
  .btn-outline {
    @apply bg-transparent border border-medical-primary text-medical-primary hover:bg-medical-primary/10 dark:border-medical-accent dark:text-medical-accent dark:hover:bg-medical-accent/10 px-6 py-3 rounded-md font-medium transition-colors;
  }
  
  .card-hover {
    @apply transition-all hover:shadow-lg hover:-translate-y-1;
  }

  /* Mobile app-specific styles */
  .mobile-app-container {
    @apply max-w-md mx-auto min-h-screen flex flex-col;
  }

  .mobile-nav-bar {
    @apply sticky top-0 z-50 px-4 py-3 flex justify-between items-center bg-white dark:bg-medical-dark-surface shadow-sm border-b border-medical-border-light dark:border-medical-dark-border;
  }

  .mobile-bottom-nav {
    @apply fixed bottom-0 left-0 right-0 z-50 bg-white dark:bg-medical-dark-surface flex justify-around items-center py-2 px-4 shadow-[0_-2px_10px_rgba(0,0,0,0.1)] dark:shadow-[0_-2px_10px_rgba(0,0,0,0.3)] border-t border-medical-border-light dark:border-medical-dark-border;
  }

  .mobile-content {
    @apply flex-grow pb-20 pt-1 px-0;
  }

  .mobile-section {
    @apply py-4 px-4;
  }

  .mobile-card {
    @apply bg-white dark:bg-medical-dark-surface rounded-xl shadow-sm p-4 border border-medical-border-light dark:border-medical-dark-border;
  }
}

/* PWA Installation prompt */
.pwa-install-prompt {
  @apply fixed bottom-20 left-4 right-4 bg-white dark:bg-medical-dark-surface p-4 rounded-lg shadow-lg border border-medical-border-light dark:border-medical-dark-border z-50 flex flex-col;
}

/* Touch-friendly button and link sizes */
.touch-target {
  @apply min-h-[44px] min-w-[44px] flex items-center justify-center;
}

/* Bottom sheet animation */
.bottom-sheet {
  @apply fixed bottom-0 left-0 right-0 bg-white dark:bg-medical-dark-surface rounded-t-2xl shadow-lg z-50 transform transition-transform duration-300 ease-in-out;
}

.bottom-sheet-enter {
  @apply translate-y-full;
}

.bottom-sheet-enter-active {
  @apply translate-y-0;
}
